import os
import csv
import logging
from filelock import FileLock
import gspread
from oauth2client.service_account import ServiceAccountCredentials

class FileManager:
    """
    Manages file operations for the crawler, including:
    - Moving data from instance-specific crawl_results.csv to final_results.csv or Google Sheets
    - Ensuring thread-safe operations with file locks
    """

    def __init__(self, root_dir=None, use_google_sheets=True, sheet_name='Social_Data'):
        """
        Initialize the FileManager.

        Args:
            root_dir: The root directory of the project. If None, it will be determined automatically.
            use_google_sheets: Whether to use Google Sheets instead of CSV files for final results.
            sheet_name: The name of the Google Sheet to use.
        """
        # Determine the root directory if not provided
        if root_dir is None:
            current_dir = os.getcwd()
            if os.path.basename(current_dir).startswith('instance_'):
                # We're in an instance directory, so the parent is the root
                self.root_dir = os.path.dirname(current_dir)
            else:
                # We're already in the root directory
                self.root_dir = current_dir
        else:
            self.root_dir = root_dir

        # Set up logging
        self.logger = logging.getLogger(__name__)

        # Define paths
        self.final_results_path = os.path.join(self.root_dir, 'final_results.csv')
        self.lock_file_path = os.path.join(self.root_dir, 'final_results.csv.lock')

        # Google Sheets configuration
        self.use_google_sheets = use_google_sheets
        self.sheet_name = sheet_name

        if self.use_google_sheets:
            try:
                # Set up Google Sheets API
                self.credentials_path = os.path.join(self.root_dir, 'credentials.json')
                self._setup_google_sheets()
                self.logger.info(f"Successfully connected to Google Sheet: {sheet_name}")
            except Exception as e:
                self.logger.error(f"Error setting up Google Sheets: {str(e)}")
                self.use_google_sheets = False
                self.logger.warning("Falling back to CSV file storage")

    def _setup_google_sheets(self):
        """
        Set up the Google Sheets API connection.
        """
        # Define the scope
        scope = [
            'https://www.googleapis.com/auth/spreadsheets',
            'https://www.googleapis.com/auth/drive'
        ]

        # Authenticate with credentials
        credentials = ServiceAccountCredentials.from_json_keyfile_name(self.credentials_path, scope)
        client = gspread.authorize(credentials)

        # Open the Google Sheet
        try:
            self.sheet = client.open(self.sheet_name).sheet1
        except gspread.exceptions.SpreadsheetNotFound:
            # Create a new sheet if it doesn't exist
            self.logger.info(f"Sheet '{self.sheet_name}' not found. Creating a new one.")
            spreadsheet = client.create(self.sheet_name)
            self.sheet = spreadsheet.sheet1

    def move_results_to_final(self, instance_id):
        """
        Move data from an instance's crawl_results.csv to the final destination (Google Sheets or CSV).

        Args:
            instance_id: The ID of the instance whose results should be moved

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            # Determine the instance directory and crawl_results.csv path
            instance_dir = os.path.join(self.root_dir, f'instance_{instance_id}')
            crawl_results_path = os.path.join(instance_dir, 'csv_files', 'crawl_results.csv')

            # Check if the crawl_results.csv file exists
            if not os.path.exists(crawl_results_path):
                self.logger.warning(f"No crawl_results.csv file found for instance {instance_id}")
                return False

            # Read the data from the crawl_results.csv file
            with open(crawl_results_path, 'r', encoding='utf-8') as crawl_file:
                reader = csv.reader(crawl_file)
                headers = next(reader)  # Get the headers
                rows = list(reader)     # Get all the data rows

            if self.use_google_sheets:
                return self._save_to_google_sheets(headers, rows, instance_id)
            else:
                return self._save_to_csv(headers, rows, instance_id)

        except Exception as e:
            self.logger.error(f"Error moving results to final destination: {str(e)}")
            return False

    def _save_to_google_sheets(self, headers, rows, instance_id):
        """
        Save data to Google Sheets.

        Args:
            headers: The CSV headers
            rows: The data rows
            instance_id: The ID of the instance whose results are being saved

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.logger.info(f"Saving data from instance {instance_id} to Google Sheet: {self.sheet_name}")

            # Check if the sheet is empty or needs headers
            existing_headers = []
            try:
                existing_headers = self.sheet.row_values(1)
            except Exception:
                pass

            if not existing_headers:
                # Sheet is empty, add headers first
                self.sheet.append_row(headers)
                self.logger.info(f"Added headers to Google Sheet: {self.sheet_name}")

            # Append all rows to the sheet
            for row in rows:
                self.sheet.append_row(row)

            self.logger.info(f"Successfully saved {len(rows)} rows from instance {instance_id} to Google Sheet")
            return True

        except Exception as e:
            self.logger.error(f"Error saving to Google Sheets: {str(e)}")
            self.logger.warning("Falling back to CSV file storage")
            return self._save_to_csv(headers, rows, instance_id)

    def _save_to_csv(self, headers, rows, instance_id):
        """
        Save data to CSV file.

        Args:
            headers: The CSV headers
            rows: The data rows
            instance_id: The ID of the instance whose results are being saved

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            self.logger.info(f"Saving data from instance {instance_id} to CSV file: {self.final_results_path}")

            # Use a file lock to prevent concurrent writes to final_results.csv
            with FileLock(self.lock_file_path):
                # Check if final_results.csv exists
                if not os.path.exists(self.final_results_path):
                    # Create the file with headers
                    with open(self.final_results_path, 'w', newline='', encoding='utf-8') as final_file:
                        writer = csv.writer(final_file)
                        writer.writerow(headers)
                        writer.writerows(rows)
                    self.logger.info(f"Created {self.final_results_path} with data from instance {instance_id}")
                else:
                    # Append to the file without headers
                    with open(self.final_results_path, 'a', newline='', encoding='utf-8') as final_file:
                        writer = csv.writer(final_file)
                        writer.writerows(rows)
                    self.logger.info(f"Appended data from instance {instance_id} to {self.final_results_path}")

            return True

        except Exception as e:
            self.logger.error(f"Error saving to CSV file: {str(e)}")
            return False
