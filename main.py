
import multiprocessing
from bot_manager import BotInstanceManager
import logging
from datetime import datetime
import os
import signal
import sys
import time
import shutil
from filelock import FileLock

# Define a standalone function for running bot instances
# This avoids pickling issues with class methods
def run_bot_instance_process(instance_id):
    try:
        # Setup logging for this process
        log_dir = 'logs'
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        logging.basicConfig(
            filename=f'logs/bot_instance_{instance_id}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log',
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        logger = logging.getLogger()
        logger.addHandler(logging.StreamHandler())

        logger.info(f"Starting bot instance {instance_id}")

        # Setup instance directory
        instance_dir = f'instance_{instance_id}'
        if not os.path.exists(instance_dir):
            os.makedirs(instance_dir)

        # Create csv_files directory in instance directory
        instance_csv_dir = os.path.join(instance_dir, 'csv_files')
        if not os.path.exists(instance_csv_dir):
            os.makedirs(instance_csv_dir)

        # Copy tags.csv to instance's csv_files directory
        source_tags = 'csv_files/tags.csv'
        dest_tags = os.path.join(instance_csv_dir, 'tags.csv')
        if os.path.exists(source_tags):
            shutil.copy2(source_tags, dest_tags)
            logger.info(f"Copied tags.csv to instance {instance_id}")
        else:
            logger.error("Source tags.csv not found in csv_files directory")
            return False

        # Create a .env file in the instance directory with the assigned account
        from utils.account_manager import AccountManager
        account_manager = AccountManager()
        username, password = account_manager.get_account_credentials(instance_id)

        if not username or not password:
            logger.error(f"No available Instagram account for instance {instance_id}")
            return False

        # Create instance-specific .env file
        env_file_path = os.path.join(instance_dir, '.env')
        with open(env_file_path, 'w') as env_file:
            env_file.write(f"INSTAGRAM_USERNAME={username}\n")
            env_file.write(f"INSTAGRAM_PASSWORD={password}\n")

        logger.info(f"Created .env file for instance {instance_id} with account {username}")

        # Change working directory to instance directory
        os.chdir(instance_dir)

        # Initialize and run bot manager
        bot_manager = BotInstanceManager(instance_id=instance_id)
        success = bot_manager.run_workflow()

        # Always try to copy the crawl results to the final.csv file, regardless of success
        try:
            # Print current directory for debugging
            logger.info(f"Current directory: {os.getcwd()}")

            # Define the path to the crawl_results.csv file
            # The file is saved in the instance's csv_files directory
            csv_dir = os.path.join(instance_dir, 'csv_files')
            crawl_results_path = os.path.join(csv_dir, 'crawl_results.csv')

            logger.info(f"Looking for crawl_results.csv at: {crawl_results_path}")

            # Check if the directory and file exist
            if os.path.exists(csv_dir):
                logger.info(f"Files in csv_files directory: {os.listdir(csv_dir)}")

            if os.path.exists(crawl_results_path):
                logger.info(f"Found crawl_results.csv for instance {instance_id}")

                # Create the final.csv file in the root directory
                # Get the root directory (parent of the instance directory)
                root_dir = os.path.dirname(os.path.abspath(instance_dir))
                final_csv_path = os.path.join(root_dir, 'final.csv')
                final_lock_path = os.path.join(root_dir, 'final.csv.lock')

                logger.info(f"Will save to final.csv at: {final_csv_path}")

                # Use a file lock to prevent concurrent writes
                with FileLock(final_lock_path):
                    # Read the crawl_results.csv file
                    with open(crawl_results_path, 'r', encoding='utf-8') as crawl_file:
                        crawl_data = crawl_file.read()

                    # Check if final.csv exists
                    if not os.path.exists(final_csv_path):
                        # Create the file with headers
                        with open(final_csv_path, 'w', encoding='utf-8') as final_file:
                            # Write the entire content including headers
                            final_file.write(crawl_data)
                        logger.info(f"Created final.csv with data from instance {instance_id}")
                    else:
                        # Append to the file without headers
                        with open(crawl_results_path, 'r', encoding='utf-8') as crawl_file:
                            # Skip the header row
                            lines = crawl_file.readlines()
                            if len(lines) > 1:  # Make sure there's data beyond the header
                                with open(final_csv_path, 'a', encoding='utf-8') as final_file:
                                    # Write all lines except the header
                                    final_file.writelines(lines[1:])
                                logger.info(f"Appended data from instance {instance_id} to final.csv")
                            else:
                                logger.warning(f"No data to append from instance {instance_id}")
            else:
                logger.warning(f"No crawl_results.csv file found for instance {instance_id}")
        except Exception as e:
            logger.error(f"Error appending to final.csv: {str(e)}")

        # Return to parent directory
        os.chdir('..')

        # Note that we're done with this account (for logging purposes only)
        account_manager.release_account(instance_id)
        logger.info(f"Finished using Instagram account for instance {instance_id}")

        if success:
            logger.info(f"Bot instance {instance_id} completed successfully")
        else:
            logger.error(f"Bot instance {instance_id} failed")

        return success

    except Exception as e:
        print(f"Error in bot instance {instance_id}: {str(e)}")
        # Log that we're done with this account (for logging purposes only)
        try:
            from utils.account_manager import AccountManager
            AccountManager().release_account(instance_id)
            print(f"Noted that instance {instance_id} is no longer using its account")
        except Exception as e:
            print(f"Error in cleanup: {e}")
        return False

class MainManager:
    def __init__(self, num_instances=3):
        self.num_instances = num_instances
        self.processes = []
        self.setup_logging()

    def setup_logging(self):
        log_dir = 'logs'
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        logging.basicConfig(
            filename=f'logs/main_manager_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log',
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger()
        self.logger.addHandler(logging.StreamHandler())

    def signal_handler(self, signum, frame):
        self.logger.info("Received termination signal. Shutting down gracefully...")
        self.logger.info("Waiting for current instances to complete or timeout...")
        self.run_continuously = False  # Stop the continuous execution loop
        self.stop_all_instances()

    def stop_all_instances(self):
        for process in self.processes:
            if process.is_alive():
                self.logger.info(f"Terminating process {process.pid}")
                process.terminate()
                process.join()

    def start_instances(self):
        try:
            self.logger.info(f"Starting {self.num_instances} bot instances in continuous mode")
            self.logger.info("Press Ctrl+C to stop all instances and exit")

            signal.signal(signal.SIGINT, self.signal_handler)
            signal.signal(signal.SIGTERM, self.signal_handler)

            self.next_instance_id = 1  # Tracks the next ID to assign, but cycle within num_instances
            self.running_instances = 0
            self.run_continuously = True

            while self.run_continuously:
                while self.running_instances < self.num_instances and self.run_continuously:
                    # Calculate instance ID within 1 to num_instances
                    instance_id = ((self.next_instance_id - 1) % self.num_instances) + 1
                    self.next_instance_id += 1

                    process = multiprocessing.Process(
                        target=run_bot_instance_process,
                        args=(instance_id,)
                    )
                    self.processes.append(process)
                    process.start()
                    self.running_instances += 1
                    self.logger.info(f"Started process {process.pid} for bot instance {instance_id} (Running: {self.running_instances}/{self.num_instances})")

                # Check for completed processes
                for process in list(self.processes):
                    if not process.is_alive():
                        process.join(timeout=1)
                        self.processes.remove(process)
                        self.running_instances -= 1
                        self.logger.info(f"Process {process.pid} completed (Running: {self.running_instances}/{self.num_instances})")

                time.sleep(1)

        except Exception as e:
            self.logger.error(f"Error in main manager: {str(e)}")
        finally:
            self.stop_all_instances()
            self.logger.info("Main manager shutting down")

def main():
    # Ensure source csv_files directory exists
    if not os.path.exists('csv_files'):
        os.makedirs('csv_files')

    # Check if tags.csv exists in the source directory
    if not os.path.exists('csv_files/tags.csv'):
        print("Error: csv_files/tags.csv not found. Please ensure it exists before running the program.")
        sys.exit(1)

    # Initialize and start the main manager
    manager = MainManager(num_instances=1)
    manager.start_instances()

if __name__ == "__main__":
    # This is important for Windows to properly handle multiprocessing
    multiprocessing.freeze_support()
    main()
