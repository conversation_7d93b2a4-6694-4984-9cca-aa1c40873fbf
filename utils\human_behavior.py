import random
import time
import os
from dotenv import load_dotenv

load_dotenv()

class HumanBehavior:
    def __init__(self):
        # Load configuration from .env
        self.MIN_SCROLL_PAUSE = float(os.getenv('MIN_SCROLL_PAUSE', '1'))
        self.MAX_SCROLL_PAUSE = float(os.getenv('MAX_SCROLL_PAUSE', '3'))
        self.MIN_ACTION_DELAY = float(os.getenv('MIN_ACTION_DELAY', '0.1'))
        self.MAX_ACTION_DELAY = float(os.getenv('MAX_ACTION_DELAY', '0.5'))
        self.SCROLL_ITERATIONS = int(os.getenv('SCROLL_ITERATIONS', '5'))
        
        self.USER_AGENTS = os.getenv('USER_AGENTS', '').split('||')
        if not self.USER_AGENTS[0]:
            self.USER_AGENTS = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.114 Safari/537.36"
            ]

    def random_sleep(self, min_time=None, max_time=None):
        """Sleep for a random duration"""
        min_time = min_time or self.MIN_SCROLL_PAUSE
        max_time = max_time or self.MAX_SCROLL_PAUSE
        time.sleep(random.uniform(min_time, max_time))

    def human_type(self, element, text):
        """Simulate human typing with random delays"""
        for character in text:
            element.send_keys(character)
            time.sleep(random.uniform(self.MIN_ACTION_DELAY, self.MAX_ACTION_DELAY))

    def get_random_user_agent(self):
        """Get a random user agent from the list"""
        return random.choice(self.USER_AGENTS)

    def configure_driver_options(self):
        """Configure WebDriver options with human-like behavior"""
        from selenium import webdriver
        
        options = webdriver.ChromeOptions()
        
        # Random User-Agent
        options.add_argument(f"user-agent={self.get_random_user_agent()}")
        
        # Basic options
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        
        # Disable automation flags
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        return options

    def perform_human_scroll(self, driver, scroll_callback=None):
        """Perform human-like scrolling"""
        from selenium.webdriver.common.action_chains import ActionChains
        
        actions = ActionChains(driver)
        last_height = driver.execute_script("return document.body.scrollHeight")
        
        for _ in range(self.SCROLL_ITERATIONS):
            # Human-like scrolling
            scroll_amount = random.randint(500, 800)
            driver.execute_script(f"window.scrollBy(0, {scroll_amount})")
            self.random_sleep()
            
            # Random mouse movements
            actions.move_by_offset(
                random.randint(-50, 50), 
                random.randint(-50, 50)
            ).perform()
            
            # Execute callback if provided
            if scroll_callback:
                scroll_callback()
            
            # Random scroll back up
            if random.random() < 0.3:
                driver.execute_script("window.scrollBy(0, -200)")
                self.random_sleep(0.5, 1)

            new_height = driver.execute_script("return document.body.scrollHeight")
            if new_height == last_height:
                break
            last_height = new_height