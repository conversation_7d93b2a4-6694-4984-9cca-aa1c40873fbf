from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys
import time
import csv
import json
import os
import sys
import random
from datetime import datetime

# Get the instance ID from the current directory name
current_dir = os.path.basename(os.getcwd())
instance_id = None
if current_dir.startswith('instance_'):
    try:
        instance_id = int(current_dir.split('_')[1])
    except:
        pass

# Import the account manager
if current_dir.startswith('instance_'):
    sys.path.append(os.path.dirname(os.getcwd()))
from utils.account_manager import AccountManager

# Try to get account credentials for this instance
try:
    account_manager = AccountManager()
    username, password = account_manager.get_account_credentials(instance_id)

    if not username or not password:
        # If running directly (not through main manager), try to get any available account
        if instance_id is None:
            print("No instance ID found, trying to get any available account...")
            # Try to get the first account from accounts.json as fallback
            try:
                with open(account_manager.accounts_file, 'r') as f:
                    accounts_data = json.load(f)
                    if accounts_data.get('accounts') and len(accounts_data['accounts']) > 0:
                        username = accounts_data['accounts'][0].get('username')
                        password = accounts_data['accounts'][0].get('password')
                        print(f"Using first account from accounts.json: {username}")
            except Exception as e:
                print(f"Error reading accounts.json: {e}")

        # If still no credentials, exit
        if not username or not password:
            print(f"Error: No Instagram account available")
            sys.exit(1)
except Exception as e:
    print(f"Error getting account credentials: {e}")
    # Try to load from .env file as fallback
    try:
        from dotenv import load_dotenv
        load_dotenv()
        username = os.getenv('INSTAGRAM_USERNAME')
        password = os.getenv('INSTAGRAM_PASSWORD')
        if username and password:
            print(f"Using credentials from .env file: {username}")
        else:
            print("No credentials found in .env file either. Exiting.")
            sys.exit(1)
    except:
        print("Failed to load credentials from any source. Exiting.")
        sys.exit(1)

print(f"Using Instagram account: {username}")


def get_random_tag_from_csv(csv_path='csv_files/tags.csv'):
    """
    Read tags from CSV file and return a single random tag.

    Args:
        csv_path: Path to the CSV file containing tags

    Returns:
        A single randomly selected tag
    """
    try:
        # Check if the file exists
        if not os.path.exists(csv_path):
            print(f"Error: Tags file not found at {csv_path}")
            return "reseller"  # Default fallback tag

        # Read tags from CSV
        tags = []
        with open(csv_path, 'r', encoding='utf-8') as csvfile:
            reader = csv.reader(csvfile)
            # Skip header if it exists
            try:
                header = next(reader)
                # Check if this is actually a header or data
                if not any(h.lower() in ['tag', 'tags', 'hashtag', 'hashtags'] for h in header):
                    tags.append(header[0])  # It was data, not a header
            except StopIteration:
                pass  # Empty file

            # Read the rest of the tags
            for row in reader:
                if row and row[0].strip():  # Check if row is not empty
                    tags.append(row[0].strip())

        if not tags:
            print("No tags found in the CSV file. Using default tag.")
            return "reseller"  # Default fallback tag

        # Select a single random tag
        selected_tag = random.choice(tags)

        print(f"Selected tag: #{selected_tag}")
        return selected_tag

    except Exception as e:
        print(f"Error reading tags from CSV: {e}")
        return "reseller"  # Default fallback tag


def scrape_instagram_post_links(hashtag):
    # Set up Chromium options for headless mode
    chrome_options = webdriver.ChromeOptions()

    # Create a unique user data directory for this instance
    # This ensures each Chrome instance has its own separate profile
    if instance_id is not None:
        user_data_dir = f"chrome_data_{instance_id}"
        chrome_options.add_argument(f"--user-data-dir={os.path.abspath(user_data_dir)}")
    else:
        # For standalone execution, use a random temporary directory
        import tempfile
        temp_dir = tempfile.mkdtemp(prefix="chrome_data_")
        chrome_options.add_argument(f"--user-data-dir={temp_dir}")

    # Disable shared memory usage
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")

    # Disable GPU for headless operation
    chrome_options.add_argument("--disable-gpu")

    # Disable extensions and other features that might share data
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-background-networking")
    chrome_options.add_argument("--disable-default-apps")
    chrome_options.add_argument("--disable-sync")
    chrome_options.add_argument("--disable-translate")
    chrome_options.add_argument("--metrics-recording-only")
    chrome_options.add_argument("--mute-audio")
    chrome_options.add_argument("--no-first-run")

    # Add a custom user agent to further differentiate instances
    chrome_options.add_argument(f"--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Instance/{instance_id if instance_id else 'standalone'}")

    # Run in headless mode for server environments
    # chrome_options.add_argument('--headless=new')

    # Additional arguments for running in server/Docker environments
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')

    print(f"Starting Chrome with persistent profile for instance {instance_id if instance_id else 'standalone'}")

    try:
        # Initialize the WebDriver with Chromium
        from selenium.webdriver.chrome.service import Service
        service = Service()
        driver = webdriver.Chrome(options=chrome_options, service=service)
    except Exception as e:
        print(f"Error initializing Chrome WebDriver: {str(e)}")
        # Try with explicit path to chromedriver
        try:
            service = Service('/usr/local/bin/chromedriver')
            driver = webdriver.Chrome(options=chrome_options, service=service)
        except Exception as e2:
            print(f"Second attempt failed: {str(e2)}")
            # Last resort - try with log output
            service = Service(log_output=os.path.join(os.getcwd(), 'chromedriver.log'))
            driver = webdriver.Chrome(options=chrome_options, service=service)

    # Create a WebDriverWait object
    wait = WebDriverWait(driver, 10)

    # Check if already logged in by checking for the ds_user_id cookie
    try:
        print("Checking if already logged in...")
        driver.get('https://www.instagram.com/')
        time.sleep(2)

        # Get all cookies
        cookies = driver.get_cookies()

        # Check if ds_user_id cookie exists (this cookie is present when logged in)
        already_logged_in = False
        for cookie in cookies:
            if cookie['name'] == 'ds_user_id':
                print(f"Found ds_user_id cookie. Account {username} is already logged in. Skipping login process.")
                already_logged_in = True
                break

        if not already_logged_in:
            print("No ds_user_id cookie found. Not logged in. Will perform login.")
    except Exception as e:
        print(f"Error checking login status: {e}")
        already_logged_in = False

    # If not already logged in, perform login
    if not already_logged_in:
        print(f"Logging in with account: {username}")
        driver.get('https://www.instagram.com/accounts/login/')

        # Wait for the login page to load
        wait.until(EC.presence_of_element_located((By.NAME, 'username')))

        # Enter login credentials
        email_input = driver.find_element(By.NAME, 'username')
        password_input = driver.find_element(By.NAME, 'password')

        # Use the credentials obtained from account manager
        # username and password are already defined at the module level
        email_input.send_keys(username)
        password_input.send_keys(password)
        password_input.send_keys(Keys.ENTER)

        # Wait for login to complete
        time.sleep(5)

        # Verify login was successful by checking for the ds_user_id cookie
        try:
            # Get all cookies
            cookies = driver.get_cookies()

            # Check if ds_user_id cookie exists
            login_successful = False
            for cookie in cookies:
                if cookie['name'] == 'ds_user_id':
                    print(f"Found ds_user_id cookie. Login successful!")
                    login_successful = True
                    break

            if not login_successful:
                print("Warning: Login may not have been successful. No ds_user_id cookie found.")
        except Exception as e:
            print(f"Error verifying login: {e}")

    # Navigate to hashtag page
    driver.get(f"https://www.instagram.com/explore/tags/{hashtag}/")
    time.sleep(5)

    post_links = set()
    try:
        # Scroll and collect post links
        for _ in range(5):  # Number of scrolls
            wait.until(EC.presence_of_all_elements_located((By.CSS_SELECTOR, "a[href*='/p/']")))
            posts = driver.find_elements(By.CSS_SELECTOR, "a[href*='/p/']")

            # Extract post links
            for post in posts:
                post_links.add(post.get_attribute("href"))

            # Scroll down the page
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)

    except Exception as e:
        print(f"Error during scraping: {e}")
    finally:
        # Close the driver
        driver.quit()

        # Clean up temporary directory if it was created for standalone execution
        if instance_id is None and 'temp_dir' in locals():
            try:
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)
                print(f"Cleaned up temporary Chrome profile directory: {temp_dir}")
            except Exception as e:
                print(f"Error cleaning up temporary directory: {e}")

    return list(post_links)


if __name__ == "__main__":
    # Get a random tag from the instance's tags.csv file
    random_tag = get_random_tag_from_csv()

    # Scrape Instagram posts for the selected tag
    post_links = scrape_instagram_post_links(random_tag)

    print(f"Extracted {len(post_links)} Post Links for tag #{random_tag}")

    # Save links to CSV file
    filename = f"csv_files/instagram_posts.csv"
    with open(filename, 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(['Post URL'])  # Header
        for link in post_links:
            writer.writerow([link])

    print(f"Post links have been saved to {filename}")
    print(f"Total posts collected: {len(post_links)}")
