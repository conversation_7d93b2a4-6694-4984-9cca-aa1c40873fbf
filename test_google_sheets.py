import os
import sys
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

# Add the project root to the Python path
sys.path.append(os.getcwd())

# Import the FileManager class
from utils.file_manager import <PERSON><PERSON>anager

def test_google_sheets_integration():
    """
    Test the Google Sheets integration in the FileManager class.
    """
    logger = logging.getLogger(__name__)
    logger.info("Testing Google Sheets integration...")
    
    # Create a FileManager instance with Google Sheets enabled
    file_manager = FileManager(use_google_sheets=True, sheet_name='Social_Data')
    
    # Test if the Google Sheets connection was established
    if file_manager.use_google_sheets:
        logger.info("Successfully connected to Google Sheets!")
        
        # Test moving results from an instance to Google Sheets
        instance_id = 1  # Assuming instance_1 exists
        success = file_manager.move_results_to_final(instance_id)
        
        if success:
            logger.info(f"Successfully moved data from instance {instance_id} to Google Sheets!")
        else:
            logger.error(f"Failed to move data from instance {instance_id} to Google Sheets.")
    else:
        logger.error("Failed to connect to Google Sheets. Check your credentials and internet connection.")

if __name__ == "__main__":
    test_google_sheets_integration()
