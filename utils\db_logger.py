import psycopg2
from datetime import datetime
import json
from typing import Any, Dict, Optional
import os
from dotenv import load_dotenv

load_dotenv()

class DatabaseLogger:
    def __init__(self):
        self.conn = psycopg2.connect(
            dbname=os.getenv('DB_NAME'),
            user=os.getenv('DB_USER'),
            password=os.getenv('DB_PASSWORD'),
            host=os.getenv('DB_HOST'),
            port=os.getenv('DB_PORT', '5432')
        )
        self.create_tables()

    def create_tables(self):
        with self.conn.cursor() as cur:
            # Create log_levels table
            cur.execute("""
                CREATE TABLE IF NOT EXISTS log_levels (
                    id SERIAL PRIMARY KEY,
                    name VARCHAR(20) UNIQUE NOT NULL
                )
            """)
            
            # Insert standard log levels
            cur.execute("""
                INSERT INTO log_levels (name) 
                VALUES ('DEBUG'), ('INFO'), ('WARNING'), ('ERROR'), ('CRITICAL')
                ON CONFLICT (name) DO NOTHING
            """)

            # Create main logging table
            cur.execute("""
                CREATE TABLE IF NOT EXISTS bot_logs (
                    id SERIAL PRIMARY KEY,
                    timestamp TIMESTAMP NOT NULL,
                    level_id INTEGER REFERENCES log_levels(id),
                    instance_id INTEGER,
                    component VARCHAR(100),
                    message TEXT,
                    extra_data JSONB,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Create bot_instances table
            cur.execute("""
                CREATE TABLE IF NOT EXISTS bot_instances (
                    id SERIAL PRIMARY KEY,
                    start_time TIMESTAMP NOT NULL,
                    end_time TIMESTAMP,
                    status VARCHAR(20),
                    instance_number INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            self.conn.commit()

    def log(self, level: str, message: str, instance_id: Optional[int] = None, 
            component: str = "main", extra_data: Optional[Dict[str, Any]] = None):
        try:
            with self.conn.cursor() as cur:
                # Get level_id
                cur.execute("SELECT id FROM log_levels WHERE name = %s", (level.upper(),))
                level_id = cur.fetchone()[0]

                # Insert log entry
                cur.execute("""
                    INSERT INTO bot_logs (timestamp, level_id, instance_id, component, message, extra_data)
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (
                    datetime.now(),
                    level_id,
                    instance_id,
                    component,
                    message,
                    json.dumps(extra_data) if extra_data else None
                ))
                self.conn.commit()
        except Exception as e:
            print(f"Error logging to database: {e}")

    def record_instance_start(self, instance_number: int) -> int:
        with self.conn.cursor() as cur:
            cur.execute("""
                INSERT INTO bot_instances (start_time, status, instance_number)
                VALUES (%s, %s, %s)
                RETURNING id
            """, (datetime.now(), 'RUNNING', instance_number))
            instance_id = cur.fetchone()[0]
            self.conn.commit()
            return instance_id

    def record_instance_end(self, instance_id: int, status: str):
        with self.conn.cursor() as cur:
            cur.execute("""
                UPDATE bot_instances
                SET end_time = %s, status = %s
                WHERE id = %s
            """, (datetime.now(), status, instance_id))
            self.conn.commit()

    def __del__(self):
        if hasattr(self, 'conn'):
            self.conn.close()