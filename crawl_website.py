import csv
import re
import time
from collections import deque
from urllib.parse import urljoin, urlparse
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC


class AdvancedCrawler:
    def __init__(self, headless=False):
        self.options = Options()
        self.options.add_argument("--no-sandbox")
        self.options.add_argument("--disable-dev-shm-usage")
        if headless:
            self.options.add_argument("--headless=new")

        self.driver = webdriver.Chrome(options=self.options)
        self.visited = set()
        self.results = {}
        self.social_patterns = {
            "facebook": r"facebook\.com/[^/]+",
            "twitter": r"twitter\.com/[^/]+",
            "x": r"x\.com/[^/]+",
            "linkedin": r"linkedin\.com/(in|company)/[^/]+",
            "instagram": r"instagram\.com/[^/]+",
        }

    def classify_website(self, text, html, url):
        brand_keywords = ['about us', 'our story', 'our products', 'collection',
                         'warranty', 'company', 'philosophy', 'mission statement']
        retail_keywords = ['shop', 'buy online', 'store', 'cart', 'checkout',
                          'brands we carry', 'brands', 'add to cart', 'shipping']

        brand_score = 0
        retail_score = 0

        lower_text = text.lower()
        lower_url = url.lower()

        # Keyword matching in text
        for kw in brand_keywords:
            brand_score += lower_text.count(kw) * 2

        for kw in retail_keywords:
            retail_score += lower_text.count(kw) * 2

        # URL analysis
        if any(kw in lower_url for kw in ['shop', 'store', 'retail']):
            retail_score += 3
        elif any(kw in lower_url for kw in ['brand', 'official']):
            brand_score += 3

        # Shopping cart detection
        cart_pattern = r'(<a[^>]+href=["\'][^"\']*/(cart|checkout)/?["\'][^>]*>)|(<button[^>]*>.*add to cart.*</button>)'
        if re.search(cart_pattern, html, re.I | re.DOTALL):
            retail_score += 5

        # Brand mentions analysis
        brand_mentions = len(re.findall(r'\b(brands?\b.+?\bcarry|carry.+?\bbrands?|featuring\b)', lower_text))
        retail_score += brand_mentions * 2

        # Determine classification
        if retail_score > brand_score:
            return 'Retail'
        elif brand_score > retail_score:
            return 'Brand'
        return 'Undetermined'

    def extract_emails(self, text):
        email_regex = r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b"
        return set(re.findall(email_regex, text, re.IGNORECASE))

    def extract_phones(self, text):
        phone_regex = r"\b(?:\+?\d{1,3}[-.●]?)?\(?\d{3}\)?[-.●]?\d{3}[-.●]?\d{4}\b"
        return set(re.findall(phone_regex, text))

    def extract_social_links(self, html):
        social_links = {platform: [] for platform in self.social_patterns}
        for platform, pattern in self.social_patterns.items():
            matches = re.findall(
                rf'href=["\'](https?://(?:www\.)?{pattern}[^"\']*)', html, re.I
            )
            social_links[platform] = list(set(matches))
        return social_links

    def process_page(self, url, current_depth, max_depth):
        if url in self.visited or current_depth > max_depth:
            return []

        print(f"\nProcessing: {url} (Depth {current_depth})")
        self.visited.add(url)

        try:
            self.driver.get(url)
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            # Scroll to load dynamic content
            for _ in range(3):
                self.driver.execute_script(
                    "window.scrollTo(0, document.body.scrollHeight);"
                )
                time.sleep(1.5)

            page_text = self.driver.page_source
            visible_text = self.driver.find_element(By.TAG_NAME, "body").text

            # Extract data and classify
            self.results[url] = {
                "emails": self.extract_emails(visible_text),
                "phones": self.extract_phones(visible_text),
                "social_links": self.extract_social_links(page_text),
                "classification": self.classify_website(visible_text, page_text, url)
            }

            # Find new links
            new_links = []
            for link in self.driver.find_elements(By.TAG_NAME, "a"):
                try:
                    href = link.get_attribute("href")
                    if href and href not in self.visited:
                        abs_url = urljoin(url, href)
                        if self.is_valid_url(abs_url):
                            new_links.append(abs_url)
                except:
                    continue

            return new_links

        except Exception as e:
            print(f"Error processing {url}: {str(e)}")
            return []

    def is_valid_url(self, url):
        parsed = urlparse(url)
        return parsed.scheme in ("http", "https") and parsed.netloc != ""

    def crawl(self, start_url, max_depth=2):
        queue = deque([(start_url, 0)])

        while queue:
            url, depth = queue.popleft()
            if depth > max_depth:
                continue

            new_links = self.process_page(url, depth, max_depth)
            for link in new_links:
                if link not in self.visited:
                    queue.append((link, depth + 1))

        return self.results

    def save_to_csv(self, filename="csv_files/crawl_results.csv", source_data=None):
        print("\nSaving results to CSV...")
        with open(filename, "w", newline="", encoding="utf-8") as csvfile:
            fieldnames = [
                "Username",
                "First Name",
                "Last Name",
                "Website Links",
                "Email from Instagram",
                "Followers",
                "URL",
                "Emails from Website",
                "Phones",
                "Facebook",
                "Twitter",
                "LinkedIn",
                "Instagram",
                "Classification"
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for url, data in self.results.items():
                source_row = next(
                    (row for row in source_data if row["Website Links"] == url),
                    None
                )

                # Convert any tuples to strings in social links
                facebook_links = [str(link) for link in data["social_links"]["facebook"]]
                twitter_links = [str(link) for link in data["social_links"]["twitter"]]
                # x_links = [str(link) for link in data["social_links"]["x"]]
                linkedin_links = [str(link) for link in data["social_links"]["linkedin"]]
                instagram_links = [str(link) for link in data["social_links"]["instagram"]]

                row = {
                    "Username": source_row["Username"] if source_row else "",
                    "First Name": source_row["First Name"] if source_row else "",
                    "Last Name": source_row["Last Name"] if source_row else "",
                    "Website Links": url,
                    "Email from Instagram": source_row["Email"] if source_row else "",
                    "Followers": source_row["Followers"] if source_row else "",
                    "URL": url,
                    "Emails from Website": "; ".join(data["emails"]),
                    "Phones": "; ".join(data["phones"]),
                    "Facebook": "; ".join(facebook_links),
                    "Twitter": "; ".join(twitter_links),
                    # "X": "; ".join(x_links),
                    "LinkedIn": "; ".join(linkedin_links),
                    "Instagram": "; ".join(instagram_links),
                    "Classification": data.get("classification", "")
                }
                writer.writerow(row)

        print(f"Data saved to {filename}")


def read_data_from_csv(csv_filename):
    data_rows = []
    with open(csv_filename, newline="", encoding="utf-8") as csvfile:
        reader = csv.DictReader(csvfile)
        for row in reader:
            website_link = row.get("Website Links", "").strip()
            if website_link:
                data_rows.append({
                    "Username": row.get("Username", ""),
                    "First Name": row.get("First Name", ""),
                    "Last Name": row.get("Last Name", ""),
                    "Website Links": website_link,
                    "Email": row.get("Email", ""),
                    "Followers": row.get("Followers", "")
                })
    return data_rows


if __name__ == "__main__":
    csv_filename = "csv_files/instagram_details_final.csv"
    source_data = read_data_from_csv(csv_filename)

    if not source_data:
        print("No website links found in the CSV file.")
    else:
        crawler = AdvancedCrawler(headless=False)

        for row in source_data:
            url = row["Website Links"]
            print(f"\nStarting crawl for: {url} (Instagram user: {row['Username']})")
            crawler.crawl(url, max_depth=0)

        crawler.save_to_csv(source_data=source_data)
        crawler.driver.quit()