# Instagram Bot with Multiple Account Rotation

This project is an Instagram bot that uses multiple accounts to gather data from Instagram posts, extract usernames, collect user details, and crawl associated websites.

## Requirements

- Python 3.8+
- Chrome browser
- ChromeDriver (compatible with your Chrome version)
- PostgreSQL (optional, for database logging)

## Setup Environment

### Create a conda Environment

```
conda create -n venv python=3.8
```

### Activate conda environment

```
conda activate venv
```

### Install requirements.txt

```
pip install -r requirements.txt
```

## Required Libraries

The following libraries are used in this project:
- selenium - For browser automation
- python-dotenv - For environment variable management
- httpx - For HTTP requests
- requests - For API calls
- beautifulsoup4 - For HTML parsing
- psycopg2-binary - For PostgreSQL database connection
- filelock - For managing concurrent file access

## Project Configuration

### Configure Instagram Accounts

Edit the `accounts.json` file to add your Instagram accounts:

```json
{
    "accounts": [
        {
            "username": "your_instagram_username1",
            "password": "your_instagram_password1"
        },
        {
            "username": "your_instagram_username2",
            "password": "your_instagram_password2"
        }
    ]
}
```

You can add up to 6 Instagram accounts. Each bot instance will be assigned a unique account.

### Configure Tags

Create a `csv_files/tags.csv` file with hashtags to scrape:

```
tag
fashion
travel
food
```

### Environment Variables (Optional)

Create a `.env` file in the root directory for database configuration:

```
DB_NAME=your_db_name
DB_USER=your_db_user
DB_PASSWORD=your_db_password
DB_HOST=localhost
DB_PORT=5432

# Human behavior simulation settings
MIN_SCROLL_PAUSE=1
MAX_SCROLL_PAUSE=3
MIN_ACTION_DELAY=0.1
MAX_ACTION_DELAY=0.5
SCROLL_ITERATIONS=5
```

## Running the Bot

### Run the main manager

```
python main.py
```

This will start multiple bot instances (default: 3), each with a different Instagram account.

### Individual Script Execution (for testing)

If you want to run individual scripts for testing:

```
python gatherPosts.py
python fetchUserName.py
python gatherUserDetails.py
python crawl_website.py
```

## How It Works

### Workflow

1. `gatherPosts.py` - Scrapes Instagram posts based on hashtags
2. `fetchUserName.py` - Extracts usernames from the posts
3. `gatherUserDetails.py` - Collects user details including website links
4. `crawl_website.py` - Crawls the websites to extract contact information

### Account Rotation

1. When a bot instance starts, it requests an Instagram account from the account manager
2. The account manager randomly selects an account from your pool of accounts
3. Each instance will use its assigned account throughout its execution
4. Multiple instances may use the same account if there are more instances than accounts

### Results

The bot creates a `final_results.csv` file that combines the results from all instances. Each time an instance completes the crawling process, it appends its data to this file.


<EMAIL>