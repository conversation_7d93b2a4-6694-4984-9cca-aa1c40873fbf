import json
import os
import logging
import random
from typing import Dict, Optional, Tuple

class AccountManager:
    """
    Manages Instagram account allocation across bot instances.
    Randomly selects an account from the pool for each instance.
    """

    def __init__(self, accounts_file: str = 'accounts.json'):
        # Get the root directory (parent of the instance directory)
        if os.path.basename(os.getcwd()).startswith('instance_'):
            root_dir = os.path.dirname(os.getcwd())
        else:
            root_dir = os.getcwd()

        # Use absolute path for accounts file
        self.accounts_file = os.path.join(root_dir, accounts_file)
        self.logger = logging.getLogger(__name__)

        print(f"AccountManager using accounts file: {self.accounts_file}")

        # Cache for instance-to-account mapping (just for this session)
        self.instance_accounts = {}

    def get_account(self, instance_id: int) -> Optional[Dict[str, str]]:
        """
        Get an Instagram account for the specified bot instance.
        If an account is already assigned to this instance in this session, return that account.
        Otherwise, randomly select an account from the pool.
        """
        try:
            # Check if this instance already has an account assigned in this session
            if instance_id in self.instance_accounts:
                username = self.instance_accounts[instance_id].get('username')
                print(f"Instance {instance_id} is already using account: {username}")
                return self.instance_accounts[instance_id]

            # Load accounts data
            with open(self.accounts_file, 'r') as f:
                accounts_data = json.load(f)

            accounts = accounts_data.get('accounts', [])

            if not accounts:
                print(f"No accounts found in {self.accounts_file}")
                return None

            # Randomly select an account
            selected_account = random.choice(accounts)

            # Store the selection for this session
            self.instance_accounts[instance_id] = selected_account

            print(f"Assigned account {selected_account.get('username')} to instance {instance_id}")
            return selected_account

        except Exception as e:
            print(f"Error getting account: {str(e)}")
            return None

    def release_account(self, instance_id: int) -> bool:
        """
        Release the account assigned to the specified bot instance.
        This is just for API compatibility - it doesn't actually do anything
        since we're not locking accounts anymore.
        """
        try:
            if instance_id in self.instance_accounts:
                username = self.instance_accounts[instance_id].get('username')
                print(f"Released account {username} from instance {instance_id}")
                del self.instance_accounts[instance_id]
                return True
            return False

        except Exception as e:
            print(f"Error releasing account: {str(e)}")
            return False

    def get_account_credentials(self, instance_id: int) -> Tuple[Optional[str], Optional[str]]:
        """
        Get the username and password for an account for this instance.
        Returns a tuple of (username, password) or (None, None) if no account is available.
        """
        account = self.get_account(instance_id)
        if account:
            return account.get('username'), account.get('password')
        return None, None
