import subprocess
import time
import os
import sys
from datetime import datetime
import logging

# Add parent directory to path to import utils
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

from utils.file_manager import FileManager

class BotInstanceManager:
    def __init__(self, instance_id=None):
        self.instance_path = os.getcwd()
        self.instance_id = instance_id
        self.setup_logging()

    def setup_logging(self):
        log_dir = os.path.join(self.instance_path, 'logs')
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        logging.basicConfig(
            filename=os.path.join(log_dir, f'bot_manager_{datetime.now().strftime("%Y%m%d_%H%M%S")}.log'),
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s'
        )
        self.logger = logging.getLogger()
        self.logger.addHandler(logging.StreamHandler())

    def run_script(self, script_name):
        try:
            # Get the absolute path of the script from parent directory
            script_path = os.path.join(os.path.dirname(self.instance_path), script_name)

            self.logger.info(f"Starting {script_name}")
            # Use 'python' command for Windows compatibility
            process = subprocess.Popen(
                ['python', script_path],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=self.instance_path  # Run in instance directory
            )

            stdout, stderr = process.communicate()

            if process.returncode == 0:
                self.logger.info(f"Successfully completed {script_name}")
                return True
            else:
                self.logger.error(f"Error in {script_name}: {stderr.decode()}")
                return False

        except Exception as e:
            self.logger.error(f"Failed to run {script_name}: {str(e)}")
            return False

    def check_file_exists(self, filename):
        file_path = os.path.join(self.instance_path, filename)
        if not os.path.exists(file_path):
            self.logger.error(f"Required file {filename} not found")
            return False
        return True

    def run_workflow(self):
        self.logger.info("Starting bot workflow")

        # Define the workflow steps
        workflow = [
            {
                'script': 'gatherPosts.py',
                'required_file': 'csv_files/tags.csv',
                'output_file': 'csv_files/instagram_posts.csv',
                'delay': 5
            },
            {
                'script': 'fetchUserName.py',
                'required_file': 'csv_files/instagram_posts.csv',
                'output_file': 'csv_files/instagram_usernames.csv',
                'delay': 5
            },
            {
                'script': 'gatherUserDetails.py',
                'required_file': 'csv_files/instagram_usernames.csv',
                'output_file': 'csv_files/instagram_details_final.csv',
                'delay': 5
            },
            {
                'script': 'crawl_website.py',
                'required_file': 'csv_files/instagram_details_final.csv',
                'output_file': 'csv_files/crawl_results.csv',
                'delay': 2
            }
        ]

        for step in workflow:
            # Check if required input file exists
            if not self.check_file_exists(step['required_file']):
                self.logger.error(f"Workflow stopped: missing {step['required_file']}")
                return False

            # Run the script
            if not self.run_script(step['script']):
                self.logger.error(f"Workflow stopped at {step['script']}")
                return False

            # Wait for output file to be created
            timeout = 60  # 1 minute timeout
            start_time = time.time()
            while not os.path.exists(step['output_file']):
                if time.time() - start_time > timeout:
                    self.logger.error(f"Timeout waiting for {step['output_file']}")
                    return False
                time.sleep(1)

            # Add delay between scripts
            self.logger.info(f"Waiting {step['delay']} seconds before next step...")
            time.sleep(step['delay'])

        # Move the crawl results to the final_results.csv file
        if self.instance_id is not None:
            self.logger.info(f"Moving crawl results to final_results.csv for instance {self.instance_id}")
            file_manager = FileManager()
            if file_manager.move_results_to_final(self.instance_id):
                self.logger.info("Successfully moved crawl results to final_results.csv")
            else:
                self.logger.warning("Failed to move crawl results to final_results.csv")

        self.logger.info("Workflow completed successfully")
        return True

if __name__ == "__main__":
    manager = BotInstanceManager()
    manager.run_workflow()
