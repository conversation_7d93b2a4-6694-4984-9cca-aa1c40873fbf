import re
import csv
import json
import time
import glob
import os
import sys
import random

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.keys import Keys

# Get the instance ID from the current directory name
current_dir = os.path.basename(os.getcwd())
instance_id = None
if current_dir.startswith('instance_'):
    try:
        instance_id = int(current_dir.split('_')[1])
    except:
        pass

# Import the account manager
if current_dir.startswith('instance_'):
    sys.path.append(os.path.dirname(os.getcwd()))
from utils.account_manager import AccountManager

# Try to get account credentials for this instance
try:
    account_manager = AccountManager()
    username, password = account_manager.get_account_credentials(instance_id)

    if not username or not password:
        # If running directly (not through main manager), try to get any available account
        if instance_id is None:
            print("No instance ID found, trying to get any available account...")
            # Try to get the first account from accounts.json as fallback
            try:
                with open(account_manager.accounts_file, 'r') as f:
                    accounts_data = json.load(f)
                    if accounts_data.get('accounts') and len(accounts_data['accounts']) > 0:
                        username = accounts_data['accounts'][0].get('username')
                        password = accounts_data['accounts'][0].get('password')
                        print(f"Using first account from accounts.json: {username}")
            except Exception as e:
                print(f"Error reading accounts.json: {e}")

        # If still no credentials, exit
        if not username or not password:
            print(f"Error: No Instagram account available")
            sys.exit(1)
except Exception as e:
    print(f"Error getting account credentials: {e}")
    # Try to load from .env file as fallback
    try:
        from dotenv import load_dotenv
        load_dotenv()
        username = os.getenv('INSTAGRAM_USERNAME')
        password = os.getenv('INSTAGRAM_PASSWORD')
        if username and password:
            print(f"Using credentials from .env file: {username}")
        else:
            print("No credentials found in .env file either. Exiting.")
            sys.exit(1)
    except:
        print("Failed to load credentials from any source. Exiting.")
        sys.exit(1)

print(f"Using Instagram account: {username}")


def setup_chrome_driver():
    """
    Set up Chrome WebDriver with the same configuration as gatherPosts.py
    Returns tuple of (driver, temp_dir) where temp_dir is None if using persistent profile
    """
    chrome_options = webdriver.ChromeOptions()
    temp_dir = None

    # Create a unique user data directory for this instance
    # This ensures each Chrome instance has its own separate profile
    if instance_id is not None:
        user_data_dir = f"chrome_data_{instance_id}"
        chrome_options.add_argument(f"--user-data-dir={os.path.abspath(user_data_dir)}")
    else:
        # For standalone execution, use a random temporary directory
        import tempfile
        temp_dir = tempfile.mkdtemp(prefix="chrome_data_")
        chrome_options.add_argument(f"--user-data-dir={temp_dir}")

    # Disable shared memory usage
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")

    # Disable GPU for headless operation
    chrome_options.add_argument("--disable-gpu")

    # Disable extensions and other features that might share data
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-background-networking")
    chrome_options.add_argument("--disable-default-apps")
    chrome_options.add_argument("--disable-sync")
    chrome_options.add_argument("--disable-translate")
    chrome_options.add_argument("--metrics-recording-only")
    chrome_options.add_argument("--mute-audio")
    chrome_options.add_argument("--no-first-run")

    # Add a custom user agent to further differentiate instances
    chrome_options.add_argument(f"--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Instance/{instance_id if instance_id else 'standalone'}")

    # Run in headless mode for server environments
    # chrome_options.add_argument('--headless=new')

    # Additional arguments for running in server/Docker environments
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    chrome_options.add_argument('--disable-gpu')

    print(f"Starting Chrome with persistent profile for instance {instance_id if instance_id else 'standalone'}")

    try:
        # Initialize the WebDriver with Chromium
        service = Service()
        driver = webdriver.Chrome(options=chrome_options, service=service)
    except Exception as e:
        print(f"Error initializing Chrome WebDriver: {str(e)}")
        # Try with explicit path to chromedriver
        try:
            service = Service('/usr/local/bin/chromedriver')
            driver = webdriver.Chrome(options=chrome_options, service=service)
        except Exception as e2:
            print(f"Second attempt failed: {str(e2)}")
            # Last resort - try with log output
            service = Service(log_output=os.path.join(os.getcwd(), 'chromedriver.log'))
            driver = webdriver.Chrome(options=chrome_options, service=service)

    return driver, temp_dir

def check_and_login(driver):
    """
    Check if already logged in and login if necessary using Selenium.
    """
    wait = WebDriverWait(driver, 10)

    # Check if already logged in by checking for the ds_user_id cookie
    try:
        print("Checking if already logged in...")
        driver.get('https://www.instagram.com/')
        time.sleep(2)

        # Get all cookies
        cookies = driver.get_cookies()

        # Check if ds_user_id cookie exists (this cookie is present when logged in)
        already_logged_in = False
        for cookie in cookies:
            if cookie['name'] == 'ds_user_id':
                print(f"Found ds_user_id cookie. Account {username} is already logged in. Skipping login process.")
                already_logged_in = True
                break

        if not already_logged_in:
            print("No ds_user_id cookie found. Not logged in. Will perform login.")
    except Exception as e:
        print(f"Error checking login status: {e}")
        already_logged_in = False

    # If not already logged in, perform login
    if not already_logged_in:
        print(f"Logging in with account: {username}")
        driver.get('https://www.instagram.com/accounts/login/')

        # Wait for the login page to load
        wait.until(EC.presence_of_element_located((By.NAME, 'username')))

        # Enter login credentials
        email_input = driver.find_element(By.NAME, 'username')
        password_input = driver.find_element(By.NAME, 'password')

        # Use the credentials obtained from account manager
        email_input.send_keys(username)
        password_input.send_keys(password)
        password_input.send_keys(Keys.ENTER)

        # Wait for login to complete
        time.sleep(5)

        # Verify login was successful by checking for the ds_user_id cookie
        try:
            # Get all cookies
            cookies = driver.get_cookies()

            # Check if ds_user_id cookie exists
            login_successful = False
            for cookie in cookies:
                if cookie['name'] == 'ds_user_id':
                    print(f"Found ds_user_id cookie. Login successful!")
                    login_successful = True
                    break

            if not login_successful:
                print("Warning: Login may not have been successful. No ds_user_id cookie found.")
                return False
        except Exception as e:
            print(f"Error verifying login: {e}")
            return False

    return True


def scrape_user_with_selenium(driver, username: str):
    """
    Scrape Instagram user's profile data using Selenium.
    """
    try:
        # Navigate to user profile
        profile_url = f"https://www.instagram.com/{username}/"
        print(f"Navigating to {profile_url}")
        driver.get(profile_url)
        time.sleep(3)

        # Initialize variables
        full_name = ""
        biography = ""
        website_links = []

        # Try to extract full name from the profile
        try:
            # Look for the full name element (it's usually in a span or h1 tag)
            name_elements = driver.find_elements(By.CSS_SELECTOR, "span._ap3a._aaco._aacu._aacx._aad7._aade")
            if name_elements:
                full_name = name_elements[0].text.strip()
            else:
                # Alternative selector for full name
                name_elements = driver.find_elements(By.CSS_SELECTOR, "h1")
                if name_elements:
                    full_name = name_elements[0].text.strip()
        except Exception as e:
            print(f"Error extracting full name: {e}")

        # Try to extract biography
        try:
            # Look for biography text
            bio_elements = driver.find_elements(By.CSS_SELECTOR, "div._ac69 span")
            if bio_elements:
                biography = bio_elements[0].text.strip()
            else:
                # Alternative selector for bio
                bio_elements = driver.find_elements(By.CSS_SELECTOR, "span._aacl._aaco._aacu._aacx._aad7._aade")
                if bio_elements:
                    biography = bio_elements[0].text.strip()
        except Exception as e:
            print(f"Error extracting biography: {e}")

        # Try to extract website links from the dialog
        try:
            # Look for links in the profile - first try to find the website link button
            link_elements = driver.find_elements(By.CSS_SELECTOR, "a[href*='http']")
            for link_element in link_elements:
                href = link_element.get_attribute('href')
                if href and 'instagram.com' not in href and 'facebook.com' not in href:
                    website_links.append(href)

            # Also look for dialog elements with role="dialog"
            dialog_elements = driver.find_elements(By.CSS_SELECTOR, "div[role='dialog']")
            for dialog in dialog_elements:
                span_elements = dialog.find_elements(By.TAG_NAME, "span")
                for span in span_elements:
                    span_text = span.text.strip()
                    # Check if the span contains a URL
                    if span_text and ('http' in span_text or 'www.' in span_text):
                        # Extract URLs from the span text
                        urls = re.findall(r'(https?://\S+|www\.\S+)', span_text)
                        website_links.extend(urls)
        except Exception as e:
            print(f"Error extracting website links: {e}")

        # Split full_name into first and last name
        name_parts = full_name.split(' ', 1) if full_name else []
        first_name = name_parts[0] if len(name_parts) > 0 else ""
        last_name = name_parts[1] if len(name_parts) > 1 else ""

        # Extract URLs and email from biography
        bio_links = re.findall(r'(https?://\S+)', biography) if biography else []
        all_links = website_links + bio_links
        # Remove duplicates and empty strings
        all_links = list(set([link for link in all_links if link]))

        # Extract email from biography
        email_matches = re.findall(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', biography) if biography else []
        email = email_matches[0] if email_matches else ""

        print(f"Extracted data for {username}: name='{full_name}', bio_length={len(biography)}, links={len(all_links)}, email='{email}'")

        return {
            "username": username,
            "first_name": first_name,
            "last_name": last_name,
            "website_links": all_links,
            "email": email,
        }
    except Exception as e:
        print(f"Error scraping user {username}: {e}")
        return None


def scrape_user_with_followers_selenium(driver, username: str):
    """
    Scrape Instagram user's profile data using Selenium.
    Note: Followers extraction is not implemented as it requires API access.
    """
    try:
        user_data = scrape_user_with_selenium(driver, username)
        if not user_data:
            return None

        # For now, we'll set followers to empty list since extracting followers
        # requires API access or complex scraping that might get blocked
        user_data["followers"] = []

        return user_data
    except Exception as e:
        print(f"Error scraping user with followers: {e}")
        return None


def save_to_csv(data_list, filename="csv_files/instagram_details_final.csv"):
    """
    Save all user data to a single CSV file.
    """
    try:
        with open(filename, mode='w', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(
                file,
                fieldnames=["Username", "First Name", "Last Name", "Website Links", "Email", "Followers"]
            )
            writer.writeheader()

            # If data_list is a single dictionary, convert it to a list
            if isinstance(data_list, dict):
                data_list = [data_list]

            for data in data_list:
                writer.writerow({
                    "Username": data["username"],
                    "First Name": data["first_name"],
                    "Last Name": data["last_name"],
                    "Website Links": ", ".join(data["website_links"]),
                    "Email": data["email"],
                    "Followers": ", ".join(data["followers"])
                })
        print(f"Data saved to {filename}")
    except Exception as e:
        print(f"Error saving data to CSV: {e}")


# Main execution
if __name__ == "__main__":
    # Setup Chrome driver
    driver = None
    temp_dir = None

    try:
        driver, temp_dir = setup_chrome_driver()

        # Check login status and login if necessary
        if not check_and_login(driver):
            print("Failed to login. Exiting.")
            exit(1)

        # Find the most recent instagram_usernames CSV file
        csv_files = glob.glob('csv_files/instagram_usernames.csv')
        if not csv_files:
            print("No instagram_usernames CSV file found")
            exit(1)

        latest_file = max(csv_files, key=os.path.getctime)

        # Read usernames from CSV
        with open(latest_file, 'r') as csvfile:
            reader = csv.reader(csvfile)
            next(reader)  # Skip header row
            usernames = [row[1] for row in reader]  # Username is in the second column

        print(f"Found {len(usernames)} usernames to process")

        # Process each username
        user_data_list = []
        for i, username in enumerate(usernames, 1):
            print(f"Processing {i}/{len(usernames)}: {username}")

            user_data = scrape_user_with_followers_selenium(driver, username)
            if user_data:
                user_data_list.append(user_data)

            # Add delay between requests to avoid rate limiting
            time.sleep(random.uniform(2, 4))

        # Save all data to a single file
        output_file = f"csv_files/instagram_details_final.csv"
        save_to_csv(user_data_list, output_file)

        print(f"Processing complete. Results saved in {output_file}")

    except Exception as e:
        print(f"Error during execution: {e}")
    finally:
        # Clean up
        if driver:
            driver.quit()
            print("Chrome driver closed.")

        # Clean up temporary directory if it was created for standalone execution
        if instance_id is None and 'temp_dir' in locals():
            try:
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)
                print(f"Cleaned up temporary Chrome profile directory: {temp_dir}")
            except Exception as e:
                print(f"Error cleaning up temporary directory: {e}")
