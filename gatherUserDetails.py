import re
import csv
import json
import httpx
import time
import glob
import os
import sys
from datetime import datetime

# Get the instance ID from the current directory name
current_dir = os.path.basename(os.getcwd())
instance_id = None
if current_dir.startswith('instance_'):
    try:
        instance_id = int(current_dir.split('_')[1])
    except:
        pass

# Import the account manager
if current_dir.startswith('instance_'):
    sys.path.append(os.path.dirname(os.getcwd()))
from utils.account_manager import AccountManager

# Try to get account credentials for this instance
try:
    account_manager = AccountManager()
    username, password = account_manager.get_account_credentials(instance_id)

    if not username or not password:
        # If running directly (not through main manager), try to get any available account
        if instance_id is None:
            print("No instance ID found, trying to get any available account...")
            # Try to get the first account from accounts.json as fallback
            try:
                with open(account_manager.accounts_file, 'r') as f:
                    accounts_data = json.load(f)
                    if accounts_data.get('accounts') and len(accounts_data['accounts']) > 0:
                        username = accounts_data['accounts'][0].get('username')
                        password = accounts_data['accounts'][0].get('password')
                        print(f"Using first account from accounts.json: {username}")
            except Exception as e:
                print(f"Error reading accounts.json: {e}")

        # If still no credentials, exit
        if not username or not password:
            print(f"Error: No Instagram account available")
            sys.exit(1)
except Exception as e:
    print(f"Error getting account credentials: {e}")
    # Try to load from .env file as fallback
    try:
        from dotenv import load_dotenv
        load_dotenv()
        username = os.getenv('INSTAGRAM_USERNAME')
        password = os.getenv('INSTAGRAM_PASSWORD')
        if username and password:
            print(f"Using credentials from .env file: {username}")
        else:
            print("No credentials found in .env file either. Exiting.")
            sys.exit(1)
    except:
        print("Failed to load credentials from any source. Exiting.")
        sys.exit(1)

print(f"Using Instagram account: {username}")

# Initialize the HTTP client
client = httpx.Client(
    headers={
        "x-ig-app-id": "***************",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.94 Safari/537.36",
        "Accept-Language": "en-US,en;q=0.9",
        "Accept-Encoding": "gzip, deflate, br",
        "Accept": "*/*",
    }
)

def login():
    """
    Log in to Instagram using credentials from account manager and save session cookies for subsequent requests.
    """
    try:
        # username and password are already defined at the module level from account manager
        login_url = "https://i.instagram.com/api/v1/accounts/login/"
        payload = {
            "username": username,
            "password": password,
            # "device_id": device_id
        }
        response = client.post(login_url, data=payload)

        if response.status_code == 200:
            print(f"Login successful for {username}!")
            # Save session cookies
            with open("session_cookies.json", "w") as f:
                json.dump(client.cookies.jar, f)
            return True
        else:
            print(f"Login failed: {response.status_code}, {response.text}")
            print("username: " + username)
            print("password: " + password)
            return False
    except Exception as e:
        print(f"Error during login: {e}")
        return False


def load_cookies():
    """
    Load session cookies from file to maintain the session.
    """
    try:
        with open("session_cookies.json", "r") as f:
            cookies = json.load(f)
            client.cookies.update(cookies)
        print("Session cookies loaded successfully!")
    except Exception as e:
        print(f"Error loading cookies: {e}")


def scrape_user(username: str):
    """
    Scrape Instagram user's profile data.
    """
    try:
        result = client.get(
            f"https://i.instagram.com/api/v1/users/web_profile_info/?username={username}"
        )
        data = result.json()
        user_data = data.get("data", {}).get("user", {})

        # Extract relevant fields
        full_name = user_data.get("full_name", "")
        external_url = user_data.get("external_url", "")
        biography = user_data.get("biography", "")

        # Split full_name into first and last name
        name_parts = full_name.split(' ', 1)
        first_name = name_parts[0] if len(name_parts) > 0 else ""
        last_name = name_parts[1] if len(name_parts) > 1 else ""

        # Extract URLs and email from biography
        bio_links = re.findall(r'(https?://\S+)', biography)
        all_links = [external_url] + bio_links
        all_links = [link for link in all_links if link]  # Remove empty strings

        email_matches = re.findall(r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}', biography)
        email = email_matches[0] if email_matches else ""

        return {
            "username": username,
            "first_name": first_name,
            "last_name": last_name,
            "website_links": all_links,
            "email": email,
        }
    except Exception as e:
        print(f"Error scraping user {username}: {e}")
        return None


def get_user_id(username: str):
    """
    Get the user ID for the given username.
    """
    try:
        result = client.get(
            f"https://i.instagram.com/api/v1/users/web_profile_info/?username={username}"
        )
        data = result.json()
        user_id = data.get("data", {}).get("user", {}).get("id", "")
        return user_id
    except Exception as e:
        print(f"Error fetching user ID for {username}: {e}")
        return None


def get_followers_list(user_id: str):
    """
    Get the list of followers for the given user ID.
    """
    try:
        followers = []
        next_max_id = None  # Pagination cursor
        count = 50

        while True:
            url = f"https://i.instagram.com/api/v1/friendships/{user_id}/followers/"
            if next_max_id:
                url += f"?max_id={next_max_id}"

            response = client.get(url)
            data = response.json()
            users = data.get("users", [])
            followers.extend([user.get("username", "") for user in users])

            next_max_id = data.get("next_max_id")
            if not next_max_id:
                break

            time.sleep(2)  # Delay to avoid rate-limiting

        return followers
    except Exception as e:
        print(f"Error fetching followers for user ID {user_id}: {e}")
        return []


def scrape_user_with_followers(username: str):
    """
    Scrape Instagram user's profile data along with their followers.
    """
    try:
        user_data = scrape_user(username)
        if not user_data:
            return None

        user_id = get_user_id(username)
        if user_id:
            followers = get_followers_list(user_id)
            user_data["followers"] = followers
        else:
            user_data["followers"] = []

        return user_data
    except Exception as e:
        print(f"Error scraping user with followers: {e}")
        return None


def save_to_csv(data_list, filename="csv_files/instagram_details_final.csv"):
    """
    Save all user data to a single CSV file.
    """
    try:
        with open(filename, mode='w', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(
                file,
                fieldnames=["Username", "First Name", "Last Name", "Website Links", "Email", "Followers"]
            )
            writer.writeheader()

            # If data_list is a single dictionary, convert it to a list
            if isinstance(data_list, dict):
                data_list = [data_list]

            for data in data_list:
                writer.writerow({
                    "Username": data["username"],
                    "First Name": data["first_name"],
                    "Last Name": data["last_name"],
                    "Website Links": ", ".join(data["website_links"]),
                    "Email": data["email"],
                    "Followers": ", ".join(data["followers"])
                })
        print(f"Data saved to {filename}")
    except Exception as e:
        print(f"Error saving data to CSV: {e}")


# Main execution
if __name__ == "__main__":
    # Login with credentials from .env file
    # if not login():
    #     print("Failed to login. Exiting.")
    #     exit(1)

    # Find the most recent instagram_usernames CSV file
    csv_files = glob.glob('csv_files/instagram_usernames.csv')
    if not csv_files:
        print("No instagram_usernames CSV file found")
        exit(1)

    latest_file = max(csv_files, key=os.path.getctime)

    # Read usernames from CSV
    with open(latest_file, 'r') as csvfile:
        reader = csv.reader(csvfile)
        next(reader)  # Skip header row
        usernames = [row[1] for row in reader]  # Username is in the second column

    print(f"Found {len(usernames)} usernames to process")

    # Process each username
    user_data_list = []
    for i, username in enumerate(usernames, 1):
        print(f"Processing {i}/{len(usernames)}: {username}")

        user_data = scrape_user_with_followers(username)
        if user_data:
            user_data_list.append(user_data)

        # Add delay between requests
        time.sleep(2)

    # Save all data to a single file
    output_file = f"csv_files/instagram_details_final.csv"
    save_to_csv(user_data_list, output_file)

    print(f"Processing complete. Results saved in {output_file}")
