# from selenium import webdriver
# from selenium.webdriver.chrome.service import Service
# from selenium.webdriver.common.by import By
# from selenium.webdriver.support.ui import WebDriverWait
# from selenium.webdriver.support import expected_conditions as EC
# from selenium.webdriver.common.keys import Keys
# import time
# import csv
# from datetime import datetime
# from dotenv import load_dotenv
# import os
# import glob

# load_dotenv()


# def get_username_from_post(post_url):
#     # Set up Selenium WebDriver with Firefox
#     chrome_options = webdriver.ChromeOptions()
#     chrome_options.add_argument("--disable-gpu")
#     chrome_options.add_argument("--no-sandbox")
#     # chrome_options.add_argument('--headless')
#     # service = Service("/usr/bin/msedgedriver")  # Update this path

#     # Initialize the WebDriver
#     driver = webdriver.Chrome(chrome_options)
#     driver.get("https://www.instagram.com/accounts/login/")

#     # Wait for login page to load
#     wait = WebDriverWait(driver, 10)
#     wait.until(EC.presence_of_element_located((<PERSON><PERSON>NAME, "username")))

#     # Enter login credentials
#     email_input = driver.find_element(By.NAME, "username")
#     password_input = driver.find_element(By.NAME, "password")

#     username = os.getenv("INSTAGRAM_USERNAME")
#     password = os.getenv("INSTAGRAM_PASSWORD")

#     email_input.send_keys(username)
#     password_input.send_keys(password)
#     password_input.send_keys(Keys.ENTER)

#     # Wait for login to complete
#     # wait.until(EC.presence_of_element_located((By.CLASS_NAME, '_a9--')))
#     time.sleep(5)

#     try:
#         # Navigate to the Instagram post URL
#         driver.get(post_url)
#         time.sleep(5)  # Allow time for the page to load fully

#         # Locate the username element
#         username_element = driver.find_element(
#             By.XPATH, "//a[contains(@href, '/')]"
#         )  # Adjust XPath if necessary
#         username = username_element.text

#         return username
#     except Exception as e:
#         print(f"Error: {e}")
#         return None
#     finally:
#         # Close the WebDriver
#         driver.quit()


# # # Example usage
# # post_url = "https://www.instagram.com/p/DEN2cblSVW7/"  # Replace with the actual post URL
# # username = get_username_from_post(post_url)
# # print(f"Username: {username}")


# def process_posts():
#     # Find the most recent instagram_posts CSV file
#     csv_files = glob.glob("instagram_posts.csv")
#     if not csv_files:
#         print("No instagram_posts CSV file found")
#         return

#     latest_file = max(csv_files, key=os.path.getctime)
#     usernames = []

#     # Read post URLs from CSV
#     with open(latest_file, "r") as csvfile:
#         reader = csv.reader(csvfile)
#         next(reader)  # Skip header row
#         post_urls = [row[0] for row in reader]

#     # Process each URL and get usernames
#     for url in post_urls:
#         username = get_username_from_post(url)
#         # username = "sudeep160403"
#         if username:
#             usernames.append([url, username])
#         time.sleep(2)  # Add delay to avoid rate limiting

#     # Save usernames to new CSV file
#     timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
#     output_filename = f"instagram_usernames.csv"

#     with open(output_filename, "w", newline="") as csvfile:
#         writer = csv.writer(csvfile)
#         writer.writerow(["Post URL", "Username"])  # Header
#         writer.writerows(usernames)

#     print(f"Usernames have been saved to {output_filename}")
#     print(f"Total usernames collected: {len(usernames)}")


# if __name__ == "__main__":
#     process_posts()



from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import csv
import json
import os
import sys
import glob
import random
from datetime import datetime

# Get the instance ID from the current directory name
current_dir = os.path.basename(os.getcwd())
instance_id = None
if current_dir.startswith('instance_'):
    try:
        instance_id = int(current_dir.split('_')[1])
    except:
        pass

# Import the account manager
if current_dir.startswith('instance_'):
    sys.path.append(os.path.dirname(os.getcwd()))
from utils.account_manager import AccountManager

# Try to get account credentials for this instance
try:
    account_manager = AccountManager()
    username, password = account_manager.get_account_credentials(instance_id)

    if not username or not password:
        # If running directly (not through main manager), try to get any available account
        if instance_id is None:
            print("No instance ID found, trying to get any available account...")
            # Try to get the first account from accounts.json as fallback
            try:
                with open(account_manager.accounts_file, 'r') as f:
                    accounts_data = json.load(f)
                    if accounts_data.get('accounts') and len(accounts_data['accounts']) > 0:
                        username = accounts_data['accounts'][0].get('username')
                        password = accounts_data['accounts'][0].get('password')
                        print(f"Using first account from accounts.json: {username}")
            except Exception as e:
                print(f"Error reading accounts.json: {e}")

        # If still no credentials, exit
        if not username or not password:
            print(f"Error: No Instagram account available")
            sys.exit(1)
except Exception as e:
    print(f"Error getting account credentials: {e}")
    # Try to load from .env file as fallback
    try:
        from dotenv import load_dotenv
        load_dotenv()
        username = os.getenv('INSTAGRAM_USERNAME')
        password = os.getenv('INSTAGRAM_PASSWORD')
        if username and password:
            print(f"Using credentials from .env file: {username}")
        else:
            print("No credentials found in .env file either. Exiting.")
            sys.exit(1)
    except:
        print("Failed to load credentials from any source. Exiting.")
        sys.exit(1)

print(f"Using Instagram account: {username}")


def login_instagram(driver):
    """Logs into Instagram if not already logged in."""
    # Create a WebDriverWait object
    wait = WebDriverWait(driver, 10)

    # Check if already logged in by checking for the ds_user_id cookie
    try:
        print("Checking if already logged in...")
        driver.get('https://www.instagram.com/')
        time.sleep(2)

        # Get all cookies
        cookies = driver.get_cookies()

        # Check if ds_user_id cookie exists (this cookie is present when logged in)
        for cookie in cookies:
            if cookie['name'] == 'ds_user_id':
                print(f"Found ds_user_id cookie. Account {username} is already logged in. Skipping login process.")
                return True  # Already logged in

        print("No ds_user_id cookie found. Not logged in. Will perform login.")
    except Exception as e:
        print(f"Error checking login status: {e}")

    # If we get here, we need to log in
    print(f"Logging in with account: {username}")
    driver.get("https://www.instagram.com/accounts/login/")

    # Wait for login fields
    wait.until(EC.presence_of_element_located((By.NAME, "username")))

    # Enter credentials
    email_input = driver.find_element(By.NAME, "username")
    password_input = driver.find_element(By.NAME, "password")

    # Use the credentials obtained from account manager
    # username and password are already defined at the module level
    email_input.send_keys(username)
    password_input.send_keys(password)
    password_input.send_keys(Keys.ENTER)

    # Wait for login to complete
    time.sleep(5)  # Increase if needed

    # Verify login was successful by checking for the ds_user_id cookie
    try:
        # Get all cookies
        cookies = driver.get_cookies()

        # Check if ds_user_id cookie exists
        for cookie in cookies:
            if cookie['name'] == 'ds_user_id':
                print(f"Found ds_user_id cookie. Login successful!")
                return True

        print("Warning: Login may not have been successful. No ds_user_id cookie found.")
        return False
    except Exception as e:
        print(f"Error verifying login: {e}")
        return False


def get_username_from_post(driver, post_url):
    """Retrieves the username from a post using an active session."""
    try:
        driver.get(post_url)
        time.sleep(5)  # Allow time for the page to load

        # Locate the username element
        username_element = driver.find_element(By.XPATH, "//a[contains(@href, '/')]")
        return username_element.text

    except Exception as e:
        print(f"Error fetching username from {post_url}: {e}")
        return None


def process_posts():
    """Reads posts from CSV, extracts usernames, and writes them to a new CSV."""
    csv_files = glob.glob("csv_files/instagram_posts.csv")
    if not csv_files:
        print("No instagram_posts CSV file found")
        return

    latest_file = max(csv_files, key=os.path.getctime)
    usernames = []

    with open(latest_file, "r") as csvfile:
        reader = csv.reader(csvfile)
        next(reader)  # Skip header row
        post_urls = [row[0] for row in reader]

    # Set up Selenium WebDriver with isolated profile
    chrome_options = webdriver.ChromeOptions()
    # chrome_options.add_argument('--headless')  # Run in headless mode for better performance

    # Create a unique user data directory for this instance
    if instance_id is not None:
        user_data_dir = f"chrome_data_{instance_id}"
        chrome_options.add_argument(f"--user-data-dir={os.path.abspath(user_data_dir)}")
    else:
        # For standalone execution, use a random temporary directory
        import tempfile
        temp_dir = tempfile.mkdtemp(prefix="chrome_data_")
        chrome_options.add_argument(f"--user-data-dir={temp_dir}")

    # Disable shared memory usage
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")

    # Disable GPU for headless operation
    chrome_options.add_argument("--disable-gpu")

    # Disable extensions and other features that might share data
    chrome_options.add_argument("--disable-extensions")
    chrome_options.add_argument("--disable-background-networking")
    chrome_options.add_argument("--disable-default-apps")
    chrome_options.add_argument("--disable-sync")
    chrome_options.add_argument("--disable-translate")
    chrome_options.add_argument("--metrics-recording-only")
    chrome_options.add_argument("--mute-audio")
    chrome_options.add_argument("--no-first-run")

    # Add a custom user agent to further differentiate instances
    chrome_options.add_argument(f"--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Instance/{instance_id if instance_id else 'standalone'}")

    print(f"Starting Chrome with isolated profile for instance {instance_id if instance_id else 'standalone'}")

    driver = webdriver.Chrome(options=chrome_options)

    try:
        # Login once
        login_instagram(driver)

        # Process each post
        for url in post_urls:
            username = get_username_from_post(driver, url)
            if username:
                usernames.append([url, username])
            time.sleep(2)  # Prevent rate limiting

    finally:
        driver.quit()  # Close WebDriver after all posts are processed

        # Clean up temporary directory if it was created for standalone execution
        if instance_id is None and 'temp_dir' in locals():
            try:
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)
                print(f"Cleaned up temporary Chrome profile directory: {temp_dir}")
            except Exception as e:
                print(f"Error cleaning up temporary directory: {e}")

    # Save usernames to CSV
    output_filename = "csv_files/instagram_usernames.csv"
    with open(output_filename, "w", newline="") as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(["Post URL", "Username"])
        writer.writerows(usernames)

    print(f"Usernames saved to {output_filename}")
    print(f"Total usernames collected: {len(usernames)}")


if __name__ == "__main__":
    process_posts()
