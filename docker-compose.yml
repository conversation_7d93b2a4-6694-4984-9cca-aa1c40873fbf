version: '3'

services:
  instagram-crawler:
    build:
      context: .
      dockerfile: Dockerfile
    volumes:
      - ./data:/app/data
      - ./credentials.json:/app/credentials.json
      # Mount instance directories to persist data
      - ./instance_1:/app/instance_1
    environment:
      - NUM_INSTANCES=1
      - PYTHONUNBUFFERED=1  # This ensures Python output is not buffered, so you see logs immediately
    restart: unless-stopped
    # Increase memory limit if needed
    deploy:
      resources:
        limits:
          memory: 2G
